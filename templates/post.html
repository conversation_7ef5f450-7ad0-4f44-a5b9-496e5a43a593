<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的论坛 - {{ post.title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <!-- Markdown样式 -->
    <style>
        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin: 1.5em 0 0.5em 0;
            font-weight: bold;
            line-height: 1.2;
        }
        .markdown-content h1 { font-size: 1.8em; color: #2563eb; }
        .markdown-content h2 { font-size: 1.5em; color: #3b82f6; }
        .markdown-content h3 { font-size: 1.3em; color: #6366f1; }
        .markdown-content h4 { font-size: 1.1em; color: #8b5cf6; }
        .markdown-content h5, .markdown-content h6 { font-size: 1em; color: #a855f7; }

        .markdown-content p { margin: 0.4em 0; line-height: 1.6; }
        .markdown-content ul, .markdown-content ol { margin: 0.6em 0; padding-left: 2em; }
        .markdown-content li { margin: 0.2em 0; }

        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f9fafb;
            font-style: italic;
        }

        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .markdown-content pre {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 1em;
            border-radius: 6px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            color: inherit;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #d1d5db;
            padding: 0.5em;
            text-align: left;
        }

        .markdown-content th {
            background-color: #f3f4f6;
            font-weight: bold;
        }

        .markdown-content a {
            color: #2563eb;
            text-decoration: underline;
        }

        .markdown-content a:hover {
            color: #1d4ed8;
        }

        .markdown-content hr {
            border: none;
            border-top: 2px solid #e5e7eb;
            margin: 2em 0;
        }

        .markdown-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 0.5em 0;
        }

        .markdown-content .task-list-item {
            list-style: none;
        }

        .markdown-content .task-list-item input[type="checkbox"] {
            margin-right: 0.5em;
        }
    </style>
    <script>
        // 分类导航切换函数
        function toggleCategoryNav() {
            const categoryTags = document.getElementById('categoryTags');
            const navToggleIcon = document.getElementById('navToggleIcon');
            
            if (categoryTags.style.display === 'none') {
                categoryTags.style.display = 'flex';
                navToggleIcon.textContent = '▼';
            } else {
                categoryTags.style.display = 'none';
                navToggleIcon.textContent = '▲';
            }
        }
        
        // 页面加载时初始化分类导航显示状态
        window.addEventListener('DOMContentLoaded', function() {
            // 在移动设备上默认隐藏分类标签
            if (window.innerWidth <= 768) {
                const categoryTags = document.getElementById('categoryTags');
                categoryTags.style.display = 'none';
                document.getElementById('navToggleIcon').textContent = '▲';
            }
        })
        
        // 模态框相关函数
        function showReplyModal() {
            document.getElementById('reply-modal').classList.remove('hidden');
        }

        function closeReplyModal() {
            document.getElementById('reply-modal').classList.add('hidden');
            document.querySelector('#reply-modal form').reset();
        }

        function handleDeleteReply(event, replyId) {
        if (!confirm('确定要删除这个回复吗？')) return false;

        fetch(`/reply/${replyId}/delete`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => {
            if (response.ok) {
                document.querySelector(`[data-reply-id="${replyId}"]`).remove();
            } else {
                alert('删除失败');
            }
        });
        return false;
    }

    function handleDeletePost(event, postId) {
            event.preventDefault();
            if (!confirm('确定要删除这个帖子吗？')) return;

            fetch(`/post/${postId}/delete`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/';
                    } else {
                        alert(data.message);
                    }
                });
        }

        function handleReplySubmit(event, postId) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            fetch(`/reply/${postId}`, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const replyContent = formData.get('content');
                        const replyHtml = `
                    <div class="card mb-4 border border-gray-200" data-reply-id="${data.replyId}">
                        <div class="text-gray-500 text-sm mb-2 flex justify-between">
                            <span>回复时间：${new Date().toLocaleString("sv-SE")}</span>
                            <div class="flex gap-2">
                                <button class="btn-edit text-xs px-3 py-1"
                                    onclick="showEditModal('${data.replyId}', document.getElementById('reply-${data.replyId}').textContent);">编辑</button>
                                <form action="/reply/${data.replyId}/delete" method="post">
                                    <button type="submit" class="btn-delete text-xs px-3 py-1">删除</button>
                                </form>
                            </div>
                        </div>
                        <div class="leading-relaxed text-gray-800 my-3 whitespace-pre-wrap break-words font-sans text-base tracking-wide py-2 text-justify" id="reply-${data.replyId}">${replyContent}</div>
                    </div>
                `;
                        document.querySelector('#replies-list').insertAdjacentHTML('afterbegin', replyHtml);
                        form.reset();
                        closeReplyModal();
                    } else {
                        alert('回复提交失败，请重试。');
                    }
                });
        }
        function toggleSortOrder() {
            const currentSort = "{{ current_sort }}";
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            window.location.href = `{{ url_for('view_post', post_id=post.id) }}?page={{ page }}&sort=${newSort}`;
        }

        function toggleReplyMarkdownHelp() {
            const helpDiv = document.getElementById('reply-markdown-help');
            helpDiv.classList.toggle('hidden');
        }
    </script>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <!-- 分类导航 -->
        <div class="sticky top-0 bg-white py-2 z-50 shadow-sm md:py-3" id="categoryNav">
            <div class="hidden md:block text-center py-1 bg-gray-100 rounded cursor-pointer font-bold text-gray-700 mb-1" onclick="toggleCategoryNav()">分类 <span id="navToggleIcon">▼</span></div>
            <div class="flex flex-wrap gap-2 items-center overflow-x-auto pb-1 md:gap-3" id="categoryTags">
                <a href="{{ url_for('index') }}" class="tag-item {% if not post.category %}active{% endif %} flex-shrink-0">全部</a>
                {% for cat in categories %}
                <a href="{{ url_for('index', category=cat.category) }}"
                   class="tag-item {% if post.category == cat.category %}active{% endif %} flex-shrink-0">
                    {{ cat.category }}
                    <span class="tag-count">{{ cat.count }}</span>
                </a>
                {% endfor %}
            </div>
        </div>

        <!-- 回复列表标题和排序控制 -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center my-6 py-2 border-b-2 border-gray-100 gap-4">
            <h3 class="m-0 text-lg text-gray-800 font-semibold">{{ post.title }}（{{ total_replies }}条）</h3>
            <div class="flex items-center gap-4 text-sm w-full md:w-auto">
                <button class="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-2 rounded-full cursor-pointer transition-all duration-200 text-sm font-medium flex items-center gap-1 hover:bg-gray-200 hover:border-gray-400 hover:text-gray-900 w-full md:w-auto justify-center" onclick="toggleSortOrder()">
                    {{ '时间倒序 ▼' if current_sort == 'desc' else '时间正序 ▲' }}
                </button>
            </div>
        </div>

        <!-- 帖子内容 -->
        <div class="card mb-4 border border-gray-200">
            <div class="text-gray-500 text-sm mb-2 flex justify-between">
                <span>发布时间：{{ post.created_at | beijing_time }}</span>
                <div class="flex gap-2">
                    <button class="btn-edit text-xs px-3 py-1" onclick="showPostEditModal()">编辑</button>
                    <!-- 只在无回复时显示删除按钮 -->
                    {% if replies|length == 0 %}
                    <form onsubmit="handleDeletePost(event, '{{ post.id }}')">
                        <button type="submit" class="btn-delete text-xs px-3 py-1">删除</button>
                    </form>
                    {% endif %}
                    <button onclick="showReplyModal()" class="btn-primary text-xs px-3 py-1">回复</button>
                </div>
            </div>
            <div class="leading-tight text-gray-800 font-sans text-base font-bold my-6 whitespace-pre-wrap break-words markdown-content" id="post-content">{{ post.content|markdown|safe }}</div>
        </div>


        <!-- 回复模态框 -->
        <div id="reply-modal" class="modal-overlay hidden">
            <div class="modal-content">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="m-0 text-gray-700">回复</h3>
                    <span class="text-2xl text-gray-500 cursor-pointer transition-colors duration-200 hover:text-gray-700" onclick="closeReplyModal()">&times;</span>
                </div>
                <div>
                    <form onsubmit="handleReplySubmit(event, '{{ post.id }}')" method="post">
                        <div class="mb-2">
                            <button type="button" onclick="toggleReplyMarkdownHelp()" class="text-xs text-blue-600 hover:text-blue-800 underline">Markdown语法帮助</button>
                        </div>
                        <div id="reply-markdown-help" class="hidden mb-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                            <strong>快捷语法：</strong> **粗体** *斜体* `代码` > 引用 # 标题 - 列表
                        </div>
                        <textarea name="content" placeholder="支持Markdown语法，如：**粗体** *斜体* 等..." required class="w-full h-32 text-sm leading-relaxed px-3 py-2 border border-gray-300 rounded resize-y"></textarea>
                        <div class="flex justify-end gap-2 mt-6">
                            <button type="submit" class="btn-success">提交回复</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div id="replies-list">
            {% for reply in replies %}
            <div class="card mb-4 border border-gray-200" data-reply-id="{{ reply.id }}">
                <div class="text-gray-500 text-sm mb-2 flex justify-between">
                    <span>回复时间：{{ reply.created_at | beijing_time }}</span>
                    <div class="flex gap-2">
                        {% set edit_url = url_for('edit_reply', reply_id=reply.id) %}
                        <button class="btn-edit text-xs px-3 py-1" data-reply-id="{{ reply.id }}">编辑</button>
                        <form onsubmit="return handleDeleteReply(event, {{ reply.id }})">
                            <button type="submit" class="btn-delete text-xs px-3 py-1">删除</button>
                        </form>
                    </div>
                </div>
                <div class="leading-relaxed text-gray-800 my-3 whitespace-pre-wrap break-words font-sans text-base tracking-wide py-2 text-justify markdown-content" id="reply-{{ reply.id }}">{{ reply.content|markdown|safe }}</div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            {% if total_pages > 1 %}
            {% for p in range(1, total_pages + 1) %}
            <a href="{{ url_for('view_post', post_id=post.id, page=p) }}"
                class="page-link {% if p == page %}active{% endif %}">{{ p }}</a>
            {% endfor %}
            {% endif %}
        </div>
    </div>

    <!-- 回复编辑模态框 -->
    <div id="edit-reply-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-5">
                <h3 class="m-0 text-gray-700">编辑回复</h3>
                <span class="text-2xl text-gray-500 cursor-pointer transition-colors duration-200 hover:text-gray-700 close-modal">&times;</span>
            </div>
            <div>
                <form id="edit-reply-form">
                    <textarea name="content" required class="w-full h-32 text-sm leading-relaxed px-3 py-2 border border-gray-300 rounded resize-y"></textarea>
                    <div class="flex justify-end gap-2 mt-6">
                        <button type="submit" class="btn-success">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 帖子编辑模态框 -->
    <div id="edit-post-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-5">
                <h3 class="m-0 text-gray-700">编辑帖子</h3>
                <span class="text-2xl text-gray-500 cursor-pointer transition-colors duration-200 hover:text-gray-700" onclick="closePostEditModal()">&times;</span>
            </div>
            <div>
                <form id="edit-post-form">
                    <div class="mb-4">
                        <label for="post-title" class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                        <input type="text" id="post-title" name="title" value="{{ post.title }}" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                    </div>
                    <div class="mb-4">
                        <label for="post-category" class="block text-sm font-medium text-gray-700 mb-2">分类</label>
                        <input type="text" id="post-category" name="category" value="{{ post.category }}" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                    </div>
                    <div class="mb-4">
                        <label for="post-content" class="block text-sm font-medium text-gray-700 mb-2">内容</label>
                        <textarea id="post-content" name="content" rows="12" required class="w-full px-3 py-2 border border-gray-300 rounded resize-y text-sm">{{ post.content }}</textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-6">
                        <button type="submit" class="btn-success">保存修改</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 模态框相关函数
        const modal = document.getElementById('edit-reply-modal');
        const postModal = document.getElementById('edit-post-modal');
        document.addEventListener('click', function(event) {
            if (event.target.matches('.btn-edit') && event.target.dataset.replyId) {
                event.preventDefault();
                const replyId = event.target.dataset.replyId;
                const contentElement = document.getElementById(`reply-${replyId}`);
                if (contentElement) {
                    showEditModal(replyId, contentElement.outerHTML);
                }
            }
        });

        const closeBtn = modal.querySelector('.close-modal');
        const editForm = document.getElementById('edit-reply-form');
        const postEditForm = document.getElementById('edit-post-form');
        let currentReplyId = null;

        // 点击模态框背景关闭模态框
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        postModal.addEventListener('click', function(e) {
            if (e.target === postModal) {
                closePostEditModal();
            }
        });

        async function showEditModal(replyId, content) {
            currentReplyId = replyId;
            try {
                // 从服务器获取原始内容
                const response = await fetch(`/reply/${replyId}/edit`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    editForm.querySelector('textarea').value = data.content;
                } else {
                    // 如果获取失败，使用页面上的文本内容作为备选
                    const replyContentElement = document.getElementById(`reply-${replyId}`);
                    editForm.querySelector('textarea').value = replyContentElement.textContent;
                }
            } catch (error) {
                console.error('获取原始内容失败:', error);
                // 使用页面上的文本内容作为备选
                const replyContentElement = document.getElementById(`reply-${replyId}`);
                editForm.querySelector('textarea').value = replyContentElement.textContent;
            }
            modal.classList.remove('hidden');
        }

        function closeModal() {
            modal.classList.add('hidden');
            currentReplyId = null;
            editForm.reset();
        }

        function showPostEditModal() {
            postModal.classList.remove('hidden');
        }

        function closePostEditModal() {
            postModal.classList.add('hidden');
            postEditForm.reset();
        }

        closeBtn.onclick = closeModal;

        editForm.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(editForm);
            try {
                const response = await fetch(`/reply/${currentReplyId}/edit`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                if (!response.ok) throw new Error('保存失败');
                const data = await response.json();
                
                // 更新页面中的回复内容
                const replyElement = document.querySelector(`[data-reply-id="${data.id}"]`);
                if (replyElement) {
                    const contentElement = replyElement.querySelector(`#reply-${data.id}`);
                    if (contentElement) {
                        contentElement.innerHTML = data.content;
                    }
                } else {
                    // 如果找不到元素，刷新页面
                    window.location.reload();
                }
                closeModal();
            } catch (error) {
                alert(error.message || '发生错误');
            }
        };

        // 帖子编辑表单提交处理
        postEditForm.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(postEditForm);
            try {
                const response = await fetch(`/post/{{ post.id }}/edit`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                if (!response.ok) throw new Error('保存失败');
                const data = await response.json();
                
                // 更新页面中的帖子内容
                const titleElement = document.querySelector('h3');  // 帖子标题
                const contentElement = document.getElementById('post-content');

                if (titleElement && contentElement) {
                    // 更新标题（需要保留回复数量）
                    const replyCount = titleElement.textContent.match(/（(\d+)条）/);
                    const countText = replyCount ? `（${replyCount[1]}条）` : '';
                    titleElement.textContent = data.title + countText;

                    // 更新内容（需要重新渲染markdown）
                    contentElement.innerHTML = data.content;
                } else {
                    console.error('元素未找到，正在重载页面...');
                    window.location.reload();
                }
                closePostEditModal();
            } catch (error) {
                alert(error.message || '发生错误');
            }
        };
    </script>
</body>

</html>